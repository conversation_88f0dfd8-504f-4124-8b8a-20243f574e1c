[2025-06-10T12:50:02.196Z] INFO: Database initialization started
[2025-06-10T12:50:02.198Z] INFO: JMB Pank application starting up
[2025-06-10T12:50:02.198Z] INFO: Environment: development
[2025-06-10T12:50:02.262Z] INFO: Generating new RSA key pair with ID 1
[2025-06-10T12:50:02.321Z] INFO: RSA key pair generated and stored in memory successfully
[2025-06-10T12:50:02.351Z] INFO: Central Bank Service initialized in PRODUCTION mode
[2025-06-10T12:50:02.352Z] INFO: Using Central Bank URL: https://henno.cfd/central-bank
[2025-06-10T12:50:02.379Z] INFO: Test routes enabled - FOR DEVELOPMENT USE ONLY
[2025-06-10T12:50:02.379Z] INFO: Application initialization completed
[2025-06-10T12:50:02.379Z] WARN: This is a test warning message
[2025-06-10T12:50:02.379Z] ERROR: This is a test error message - Test error
[2025-06-10T12:50:02.379Z] DEBUG: This is a debug message with details about the environment
[2025-06-10T12:50:02.381Z] INFO: JMB Pank server running on port 3000
[2025-06-10T12:50:02.382Z] INFO: API Documentation available at http://localhost:3000/api/docs
[2025-06-10T12:50:02.382Z] INFO: JWKS available at http://localhost:3000/api/transactions/jwks
[2025-06-10T12:50:02.382Z] INFO: RSA key pair generated and stored in memory successfully
[2025-06-10T12:50:02.383Z] INFO: Connected to SQLite database
[2025-06-10T12:50:02.383Z] INFO: Database tables created successfully
[2025-06-10T12:50:04.213Z] INFO: Last used bank prefix: your_bank_prefix
[2025-06-10T12:50:04.213Z] INFO: Bank prefix unchanged: your_bank_prefix
[2025-06-10T12:50:04.382Z] INFO: Sample transaction processed: transfer of €1000 from account JMB123 to account JMB456
[2025-06-10T12:50:04.382Z] INFO: User johndoe logged in successfully
[2025-06-10T12:50:04.382Z] WARN: Failed login attempt for user testuser - Invalid credentials
[2025-06-10T12:50:04.382Z] ERROR: Transaction failed - Insufficient funds
[2025-06-10T12:50:14.119Z] WARN: Authentication failed: Token expired
[2025-06-10T12:50:14.122Z] WARN: Authentication failed: Token expired
[2025-06-10T12:50:14.125Z] WARN: Authentication failed: Token expired
[2025-06-10T12:50:14.127Z] WARN: Authentication failed: Token expired
[2025-06-10T12:50:14.129Z] WARN: Authentication failed: Token expired
[2025-06-10T12:50:14.133Z] WARN: Authentication failed: Token expired
[2025-06-10T12:50:15.330Z] REQUEST: GET /favicon.ico - User: anonymous
[2025-06-10T12:50:30.509Z] WARN: Authentication failed: Token expired
[2025-06-10T12:50:57.231Z] INFO: Registration request received: {
  "username": "joonas",
  "password": "********",
  "fullName": "joonas",
  "email": "<EMAIL>"
}
[2025-06-10T12:50:57.231Z] INFO: Registration attempt for username: joonas, email: <EMAIL>
[2025-06-10T12:50:57.231Z] INFO: Creating user in database...
[2025-06-10T12:50:57.235Z] INFO: User registered successfully: joonas (ID: 1)
[2025-06-10T12:51:07.516Z] INFO: Login attempt for username: joonas
[2025-06-10T12:51:07.518Z] INFO: User joonas (ID: 1) logged in successfully, token issued
[2025-06-10T12:51:07.523Z] INFO: Token verification successful for user joonas
[2025-06-10T12:51:07.523Z] INFO: User joonas authenticated successfully for GET /user
[2025-06-10T12:51:07.526Z] INFO: Token verification successful for user joonas
[2025-06-10T12:51:07.526Z] INFO: User joonas authenticated successfully for GET /user
[2025-06-10T12:51:16.302Z] INFO: Token verification successful for user joonas
[2025-06-10T12:51:16.302Z] INFO: User joonas authenticated successfully for POST /
[2025-06-10T12:51:16.309Z] INFO: Token verification successful for user joonas
[2025-06-10T12:51:16.309Z] INFO: User joonas authenticated successfully for GET /user
