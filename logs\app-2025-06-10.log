[2025-06-10T12:50:02.196Z] INFO: Database initialization started
[2025-06-10T12:50:02.198Z] INFO: JMB Pank application starting up
[2025-06-10T12:50:02.198Z] INFO: Environment: development
[2025-06-10T12:50:02.262Z] INFO: Generating new RSA key pair with ID 1
[2025-06-10T12:50:02.321Z] INFO: RSA key pair generated and stored in memory successfully
[2025-06-10T12:50:02.351Z] INFO: Central Bank Service initialized in PRODUCTION mode
[2025-06-10T12:50:02.352Z] INFO: Using Central Bank URL: https://henno.cfd/central-bank
[2025-06-10T12:50:02.379Z] INFO: Test routes enabled - FOR DEVELOPMENT USE ONLY
[2025-06-10T12:50:02.379Z] INFO: Application initialization completed
[2025-06-10T12:50:02.379Z] WARN: This is a test warning message
[2025-06-10T12:50:02.379Z] ERROR: This is a test error message - Test error
[2025-06-10T12:50:02.379Z] DEBUG: This is a debug message with details about the environment
[2025-06-10T12:50:02.381Z] INFO: JMB Pank server running on port 3000
[2025-06-10T12:50:02.382Z] INFO: API Documentation available at http://localhost:3000/api/docs
[2025-06-10T12:50:02.382Z] INFO: JWKS available at http://localhost:3000/api/transactions/jwks
[2025-06-10T12:50:02.382Z] INFO: RSA key pair generated and stored in memory successfully
[2025-06-10T12:50:02.383Z] INFO: Connected to SQLite database
[2025-06-10T12:50:02.383Z] INFO: Database tables created successfully
[2025-06-10T12:50:04.213Z] INFO: Last used bank prefix: your_bank_prefix
[2025-06-10T12:50:04.213Z] INFO: Bank prefix unchanged: your_bank_prefix
[2025-06-10T12:50:04.382Z] INFO: Sample transaction processed: transfer of €1000 from account JMB123 to account JMB456
[2025-06-10T12:50:04.382Z] INFO: User johndoe logged in successfully
[2025-06-10T12:50:04.382Z] WARN: Failed login attempt for user testuser - Invalid credentials
[2025-06-10T12:50:04.382Z] ERROR: Transaction failed - Insufficient funds
[2025-06-10T12:50:14.119Z] WARN: Authentication failed: Token expired
[2025-06-10T12:50:14.122Z] WARN: Authentication failed: Token expired
[2025-06-10T12:50:14.125Z] WARN: Authentication failed: Token expired
[2025-06-10T12:50:14.127Z] WARN: Authentication failed: Token expired
[2025-06-10T12:50:14.129Z] WARN: Authentication failed: Token expired
[2025-06-10T12:50:14.133Z] WARN: Authentication failed: Token expired
[2025-06-10T12:50:15.330Z] REQUEST: GET /favicon.ico - User: anonymous
[2025-06-10T12:50:30.509Z] WARN: Authentication failed: Token expired
[2025-06-10T12:50:57.231Z] INFO: Registration request received: {
  "username": "joonas",
  "password": "********",
  "fullName": "joonas",
  "email": "<EMAIL>"
}
[2025-06-10T12:50:57.231Z] INFO: Registration attempt for username: joonas, email: <EMAIL>
[2025-06-10T12:50:57.231Z] INFO: Creating user in database...
[2025-06-10T12:50:57.235Z] INFO: User registered successfully: joonas (ID: 1)
[2025-06-10T12:51:07.516Z] INFO: Login attempt for username: joonas
[2025-06-10T12:51:07.518Z] INFO: User joonas (ID: 1) logged in successfully, token issued
[2025-06-10T12:51:07.523Z] INFO: Token verification successful for user joonas
[2025-06-10T12:51:07.523Z] INFO: User joonas authenticated successfully for GET /user
[2025-06-10T12:51:07.526Z] INFO: Token verification successful for user joonas
[2025-06-10T12:51:07.526Z] INFO: User joonas authenticated successfully for GET /user
[2025-06-10T12:51:16.302Z] INFO: Token verification successful for user joonas
[2025-06-10T12:51:16.302Z] INFO: User joonas authenticated successfully for POST /
[2025-06-10T12:51:16.309Z] INFO: Token verification successful for user joonas
[2025-06-10T12:51:16.309Z] INFO: User joonas authenticated successfully for GET /user
[2025-06-10T12:55:51.013Z] INFO: Database initialization started
[2025-06-10T12:55:51.015Z] INFO: JMB Pank application starting up
[2025-06-10T12:55:51.015Z] INFO: Environment: development
[2025-06-10T12:55:51.083Z] INFO: Generating new RSA key pair with ID 1
[2025-06-10T12:55:51.154Z] INFO: RSA key pair generated and stored in memory successfully
[2025-06-10T12:55:51.186Z] INFO: Central Bank Service initialized in PRODUCTION mode
[2025-06-10T12:55:51.186Z] INFO: Using Central Bank URL: https://henno.cfd/central-bank
[2025-06-10T12:55:51.216Z] INFO: Test routes enabled - FOR DEVELOPMENT USE ONLY
[2025-06-10T12:55:51.216Z] INFO: Application initialization completed
[2025-06-10T12:55:51.216Z] WARN: This is a test warning message
[2025-06-10T12:55:51.216Z] ERROR: This is a test error message - Test error
[2025-06-10T12:55:51.216Z] DEBUG: This is a debug message with details about the environment
[2025-06-10T12:55:51.218Z] INFO: JMB Pank server running on port 3000
[2025-06-10T12:55:51.218Z] INFO: API Documentation available at http://localhost:3000/api/docs
[2025-06-10T12:55:51.218Z] INFO: JWKS available at http://localhost:3000/api/transactions/jwks
[2025-06-10T12:55:51.219Z] INFO: RSA key pair generated and stored in memory successfully
[2025-06-10T12:55:51.219Z] INFO: Connected to SQLite database
[2025-06-10T12:55:51.220Z] INFO: Database tables created successfully
[2025-06-10T12:55:53.026Z] INFO: Last used bank prefix: your_bank_prefix
[2025-06-10T12:55:53.027Z] INFO: Bank prefix unchanged: your_bank_prefix
[2025-06-10T12:55:53.229Z] INFO: Sample transaction processed: transfer of €1000 from account JMB123 to account JMB456
[2025-06-10T12:55:53.229Z] INFO: User johndoe logged in successfully
[2025-06-10T12:55:53.229Z] WARN: Failed login attempt for user testuser - Invalid credentials
[2025-06-10T12:55:53.229Z] ERROR: Transaction failed - Insufficient funds
[2025-06-10T12:55:55.505Z] INFO: Token verification successful for user joonas
[2025-06-10T12:55:55.505Z] WARN: Authentication failed: Invalid session for user joonas
[2025-06-10T12:55:55.508Z] INFO: Token verification successful for user joonas
[2025-06-10T12:55:55.509Z] WARN: Authentication failed: Invalid session for user joonas
[2025-06-10T12:55:55.510Z] INFO: Token verification successful for user joonas
[2025-06-10T12:55:55.511Z] WARN: Authentication failed: Invalid session for user joonas
[2025-06-10T12:55:55.512Z] INFO: Token verification successful for user joonas
[2025-06-10T12:55:55.512Z] WARN: Authentication failed: Invalid session for user joonas
[2025-06-10T12:55:55.514Z] INFO: Token verification successful for user joonas
[2025-06-10T12:55:55.514Z] WARN: Authentication failed: Invalid session for user joonas
[2025-06-10T12:55:55.520Z] INFO: Token verification successful for user joonas
[2025-06-10T12:55:55.521Z] WARN: Authentication failed: Invalid session for user joonas
[2025-06-10T12:55:57.853Z] INFO: Token verification successful for user joonas
[2025-06-10T12:55:57.853Z] WARN: Authentication failed: Invalid session for user joonas
[2025-06-10T12:56:09.433Z] INFO: Login attempt for username: joonas
[2025-06-10T12:56:09.435Z] INFO: User joonas (ID: 1) logged in successfully, token issued
[2025-06-10T12:56:09.440Z] INFO: Token verification successful for user joonas
[2025-06-10T12:56:09.441Z] INFO: User joonas authenticated successfully for GET /user
[2025-06-10T12:56:09.443Z] INFO: Token verification successful for user joonas
[2025-06-10T12:56:09.444Z] INFO: User joonas authenticated successfully for GET /user
[2025-06-10T12:56:21.068Z] INFO: Token verification successful for user joonas
[2025-06-10T12:56:21.068Z] INFO: User joonas authenticated successfully for POST /
[2025-06-10T12:56:21.076Z] INFO: Token verification successful for user joonas
[2025-06-10T12:56:21.076Z] INFO: User joonas authenticated successfully for GET /user
[2025-06-10T13:00:38.297Z] INFO: Database initialization started
[2025-06-10T13:00:38.299Z] INFO: JMB Pank application starting up
[2025-06-10T13:00:38.299Z] INFO: Environment: development
[2025-06-10T13:00:38.370Z] INFO: Generating new RSA key pair with ID 1
[2025-06-10T13:00:38.473Z] INFO: RSA key pair generated and stored in memory successfully
[2025-06-10T13:00:38.505Z] INFO: Central Bank Service initialized in PRODUCTION mode
[2025-06-10T13:00:38.506Z] INFO: Using Central Bank URL: https://henno.cfd/central-bank
[2025-06-10T13:00:38.535Z] INFO: Test routes enabled - FOR DEVELOPMENT USE ONLY
[2025-06-10T13:00:38.535Z] INFO: Application initialization completed
[2025-06-10T13:00:38.535Z] WARN: This is a test warning message
[2025-06-10T13:00:38.535Z] ERROR: This is a test error message - Test error
[2025-06-10T13:00:38.535Z] DEBUG: This is a debug message with details about the environment
[2025-06-10T13:00:38.538Z] INFO: JMB Pank server running on port 3000
[2025-06-10T13:00:38.539Z] INFO: API Documentation available at http://localhost:3000/api/docs
[2025-06-10T13:00:38.539Z] INFO: JWKS available at http://localhost:3000/api/transactions/jwks
[2025-06-10T13:00:38.539Z] INFO: RSA key pair generated and stored in memory successfully
[2025-06-10T13:00:38.539Z] INFO: Connected to SQLite database
[2025-06-10T13:00:38.540Z] INFO: Database tables created successfully
[2025-06-10T13:00:40.310Z] INFO: Last used bank prefix: your_bank_prefix
[2025-06-10T13:00:40.310Z] INFO: Bank prefix unchanged: your_bank_prefix
[2025-06-10T13:00:40.542Z] INFO: Sample transaction processed: transfer of €1000 from account JMB123 to account JMB456
[2025-06-10T13:00:40.542Z] INFO: User johndoe logged in successfully
[2025-06-10T13:00:40.542Z] WARN: Failed login attempt for user testuser - Invalid credentials
[2025-06-10T13:00:40.542Z] ERROR: Transaction failed - Insufficient funds
[2025-06-10T13:00:43.816Z] REQUEST: GET /api/accounts/user - User: anonymous
[2025-06-10T13:00:43.819Z] INFO: Token verification successful for user joonas
[2025-06-10T13:00:43.820Z] WARN: Authentication failed: Invalid session for user joonas
[2025-06-10T13:00:43.816Z] RESPONSE: GET /user - Status: 401
[2025-06-10T13:00:43.816Z] RESPONSE ERROR: {"error":"Invalid session","code":"AUTH_008","status":401,"timestamp":"2025-06-10T13:00:43.820Z","description":"Your session has expired. Please log in again","details":{"username":"joonas","route":"/api/accounts/user"}}
[2025-06-10T13:00:43.823Z] REQUEST: GET /api/accounts/user - User: anonymous
[2025-06-10T13:00:43.825Z] INFO: Token verification successful for user joonas
[2025-06-10T13:00:43.825Z] WARN: Authentication failed: Invalid session for user joonas
[2025-06-10T13:00:43.823Z] RESPONSE: GET /user - Status: 401
[2025-06-10T13:00:43.823Z] RESPONSE ERROR: {"error":"Invalid session","code":"AUTH_008","status":401,"timestamp":"2025-06-10T13:00:43.825Z","description":"Your session has expired. Please log in again","details":{"username":"joonas","route":"/api/accounts/user"}}
[2025-06-10T13:00:43.826Z] REQUEST: GET /api/accounts/user - User: anonymous
[2025-06-10T13:00:43.827Z] INFO: Token verification successful for user joonas
[2025-06-10T13:00:43.827Z] WARN: Authentication failed: Invalid session for user joonas
[2025-06-10T13:00:43.826Z] RESPONSE: GET /user - Status: 401
[2025-06-10T13:00:43.826Z] RESPONSE ERROR: {"error":"Invalid session","code":"AUTH_008","status":401,"timestamp":"2025-06-10T13:00:43.827Z","description":"Your session has expired. Please log in again","details":{"username":"joonas","route":"/api/accounts/user"}}
[2025-06-10T13:00:43.831Z] REQUEST: GET /api/accounts/user - User: anonymous
[2025-06-10T13:00:43.832Z] INFO: Token verification successful for user joonas
[2025-06-10T13:00:43.833Z] WARN: Authentication failed: Invalid session for user joonas
[2025-06-10T13:00:43.831Z] RESPONSE: GET /user - Status: 401
[2025-06-10T13:00:43.831Z] RESPONSE ERROR: {"error":"Invalid session","code":"AUTH_008","status":401,"timestamp":"2025-06-10T13:00:43.833Z","description":"Your session has expired. Please log in again","details":{"username":"joonas","route":"/api/accounts/user"}}
[2025-06-10T13:00:43.834Z] REQUEST: GET /api/accounts/user - User: anonymous
[2025-06-10T13:00:43.835Z] INFO: Token verification successful for user joonas
[2025-06-10T13:00:43.835Z] WARN: Authentication failed: Invalid session for user joonas
[2025-06-10T13:00:43.834Z] RESPONSE: GET /user - Status: 401
[2025-06-10T13:00:43.834Z] RESPONSE ERROR: {"error":"Invalid session","code":"AUTH_008","status":401,"timestamp":"2025-06-10T13:00:43.836Z","description":"Your session has expired. Please log in again","details":{"username":"joonas","route":"/api/accounts/user"}}
[2025-06-10T13:00:43.840Z] REQUEST: GET /api/accounts/user - User: anonymous
[2025-06-10T13:00:43.841Z] INFO: Token verification successful for user joonas
[2025-06-10T13:00:43.841Z] WARN: Authentication failed: Invalid session for user joonas
[2025-06-10T13:00:43.840Z] RESPONSE: GET /user - Status: 401
[2025-06-10T13:00:43.840Z] RESPONSE ERROR: {"error":"Invalid session","code":"AUTH_008","status":401,"timestamp":"2025-06-10T13:00:43.841Z","description":"Your session has expired. Please log in again","details":{"username":"joonas","route":"/api/accounts/user"}}
[2025-06-10T13:00:44.755Z] REQUEST: DELETE /api/sessions - User: anonymous
[2025-06-10T13:00:44.755Z] REQUEST BODY: {}
[2025-06-10T13:00:44.756Z] INFO: Token verification successful for user joonas
[2025-06-10T13:00:44.757Z] WARN: Authentication failed: Invalid session for user joonas
[2025-06-10T13:00:44.755Z] RESPONSE: DELETE / - Status: 401
[2025-06-10T13:00:44.755Z] RESPONSE ERROR: {"error":"Invalid session","code":"AUTH_008","status":401,"timestamp":"2025-06-10T13:00:44.757Z","description":"Your session has expired. Please log in again","details":{"username":"joonas","route":"/api/sessions"}}
[2025-06-10T13:00:54.385Z] REQUEST: POST /api/sessions - User: anonymous
[2025-06-10T13:00:54.385Z] REQUEST BODY: {"username":"joonas","password":"********"}
[2025-06-10T13:00:54.386Z] INFO: Login attempt for username: joonas
[2025-06-10T13:00:54.389Z] INFO: User joonas (ID: 1) logged in successfully, token issued
[2025-06-10T13:00:54.385Z] RESPONSE: POST / - Status: 200
[2025-06-10T13:00:54.385Z] RESPONSE BODY: {"user":{"id":1,"username":"joonas","full_name":"joonas","email":"<EMAIL>","created_at":"2025-06-10 12:50:57"},"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.xINxNgUYMR29w0m4N1zjFul0sSksWeYmaxBwgmQmKyI"}
[2025-06-10T13:00:54.392Z] REQUEST: GET /api/accounts/user - User: anonymous
[2025-06-10T13:00:54.394Z] INFO: Token verification successful for user joonas
[2025-06-10T13:00:54.394Z] INFO: User joonas authenticated successfully for GET /user
[2025-06-10T13:00:54.392Z] RESPONSE: GET /user - Status: 200
[2025-06-10T13:00:54.392Z] RESPONSE BODY: [{"id":1,"account_number":"your_bank_prefix5e351ebe3c5c4937983c","owner_name":"joonas","balance":1000,"currency":"EUR","user_id":1,"account_type":"savings","created_at":"2025-06-10 12:51:16"},{"id":2,"account_number":"your_bank_prefix15c8e563dbe74995b995","owner_name":"joonas","balance":1000,"currency":"EUR","user_id":1,"account_type":"checking","created_at":"2025-06-10 12:56:21"}]
[2025-06-10T13:00:54.396Z] REQUEST: GET /api/accounts/user - User: anonymous
[2025-06-10T13:00:54.397Z] INFO: Token verification successful for user joonas
[2025-06-10T13:00:54.398Z] INFO: User joonas authenticated successfully for GET /user
[2025-06-10T13:00:54.396Z] RESPONSE: GET /user - Status: 200
[2025-06-10T13:00:54.396Z] RESPONSE BODY: [{"id":1,"account_number":"your_bank_prefix5e351ebe3c5c4937983c","owner_name":"joonas","balance":1000,"currency":"EUR","user_id":1,"account_type":"savings","created_at":"2025-06-10 12:51:16"},{"id":2,"account_number":"your_bank_prefix15c8e563dbe74995b995","owner_name":"joonas","balance":1000,"currency":"EUR","user_id":1,"account_type":"checking","created_at":"2025-06-10 12:56:21"}]
[2025-06-10T13:00:54.402Z] REQUEST: GET /api/transactions/account/your_bank_prefix5e351ebe3c5c4937983c - User: anonymous
[2025-06-10T13:00:54.402Z] RESPONSE: GET /account/your_bank_prefix5e351ebe3c5c4937983c - Status: 200
[2025-06-10T13:00:54.402Z] RESPONSE BODY: []
[2025-06-10T13:00:54.409Z] REQUEST: GET /api/transactions/account/your_bank_prefix15c8e563dbe74995b995 - User: anonymous
[2025-06-10T13:00:54.409Z] RESPONSE: GET /account/your_bank_prefix15c8e563dbe74995b995 - Status: 200
[2025-06-10T13:00:54.409Z] RESPONSE BODY: []
[2025-06-10T13:00:55.556Z] REQUEST: GET /api/logs?lines=100 - User: anonymous
[2025-06-10T13:00:55.556Z] RESPONSE: GET /?lines=100 - Status: 200
[2025-06-10T13:00:55.556Z] RESPONSE BODY: ["[2025-06-10T12:50:02.196Z] INFO: Database initialization started","[2025-06-10T12:50:02.198Z] INFO: JMB Pank application starting up","[2025-06-10T12:50:02.198Z] INFO: Environment: development","[2025-06-10T12:50:02.262Z] INFO: Generating new RSA key pair with ID 1","[2025-06-10T12:50:02.321Z] INFO: RSA key pair generated and stored in memory successfully","[2025-06-10T12:50:02.351Z] INFO: Central Bank Service initialized in PRODUCTION mode","[2025-06-10T12:50:02.352Z] INFO: Using Central Bank URL: https://henno.cfd/central-bank","[2025-06-10T12:50:02.379Z] INFO: Test routes enabled - FOR DEVELOPMENT USE ONLY","[2025-06-10T12:50:02.379Z] INFO: Application initialization completed","[2025-06-10T12:50:02.379Z] WARN: This is a test warning message","[2025-06-10T12:50:02.379Z] ERROR: This is a test error message - Test error","[2025-06-10T12:50:02.379Z] DEBUG: This is a debug message with details about the environment","[2025-06-10T12:50:02.381Z] INFO: JMB Pank server running on port 3000","[2025-06-10T12:50:02.382Z] INFO: API Documentation available at http://localhost:3000/api/docs","[2025-06-10T12:50:02.382Z] INFO: JWKS available at http://localhost:3000/api/transactions/jwks","[2025-06-10T12:50:02.382Z] INFO: RSA key pair generated and stored in memory successfully","[2025-06-10T12:50:02.383Z] INFO: Connected to SQLite database","[2025-06-10T12:50:02.383Z] INFO: Database tables created successfully","[2025-06-10T12:50:04.213Z] INFO: Last used bank prefix: your_bank_prefix","[2025-06-10T12:50:04.213Z] INFO: Bank prefix unchanged: your_bank_prefix","[2025-06-10T12:50:04.382Z] INFO: Sample transaction processed: transfer of €1000 from account JMB123 to account JMB456","[2025-06-10T12:50:04.382Z] INFO: User johndoe logged in successfully","[2025-06-10T12:50:04.382Z] WARN: Failed login attempt for user testuser - Invalid credentials","[2025-06-10T12:50:04.382Z] ERROR: Transaction failed - Insufficient funds","[2025-06-10T12:50:14.119Z] WARN: Authentication failed: Toke... [TRUNCATED]
[2025-06-10T13:00:59.126Z] REQUEST: GET /api/accounts/user - User: anonymous
[2025-06-10T13:00:59.127Z] INFO: Token verification successful for user joonas
[2025-06-10T13:00:59.127Z] INFO: User joonas authenticated successfully for GET /user
[2025-06-10T13:00:59.126Z] RESPONSE: GET /user - Status: 200
[2025-06-10T13:00:59.126Z] RESPONSE BODY: [{"id":1,"account_number":"your_bank_prefix5e351ebe3c5c4937983c","owner_name":"joonas","balance":1000,"currency":"EUR","user_id":1,"account_type":"savings","created_at":"2025-06-10 12:51:16"},{"id":2,"account_number":"your_bank_prefix15c8e563dbe74995b995","owner_name":"joonas","balance":1000,"currency":"EUR","user_id":1,"account_type":"checking","created_at":"2025-06-10 12:56:21"}]
[2025-06-10T13:00:59.129Z] REQUEST: GET /api/accounts/user - User: anonymous
[2025-06-10T13:00:59.129Z] INFO: Token verification successful for user joonas
[2025-06-10T13:00:59.130Z] INFO: User joonas authenticated successfully for GET /user
[2025-06-10T13:00:59.129Z] RESPONSE: GET /user - Status: 200
[2025-06-10T13:00:59.129Z] RESPONSE BODY: [{"id":1,"account_number":"your_bank_prefix5e351ebe3c5c4937983c","owner_name":"joonas","balance":1000,"currency":"EUR","user_id":1,"account_type":"savings","created_at":"2025-06-10 12:51:16"},{"id":2,"account_number":"your_bank_prefix15c8e563dbe74995b995","owner_name":"joonas","balance":1000,"currency":"EUR","user_id":1,"account_type":"checking","created_at":"2025-06-10 12:56:21"}]
[2025-06-10T13:00:59.131Z] REQUEST: GET /api/accounts/user - User: anonymous
[2025-06-10T13:00:59.131Z] INFO: Token verification successful for user joonas
[2025-06-10T13:00:59.132Z] INFO: User joonas authenticated successfully for GET /user
[2025-06-10T13:00:59.131Z] RESPONSE: GET /user - Status: 200
[2025-06-10T13:00:59.131Z] RESPONSE BODY: [{"id":1,"account_number":"your_bank_prefix5e351ebe3c5c4937983c","owner_name":"joonas","balance":1000,"currency":"EUR","user_id":1,"account_type":"savings","created_at":"2025-06-10 12:51:16"},{"id":2,"account_number":"your_bank_prefix15c8e563dbe74995b995","owner_name":"joonas","balance":1000,"currency":"EUR","user_id":1,"account_type":"checking","created_at":"2025-06-10 12:56:21"}]
[2025-06-10T13:00:59.133Z] REQUEST: GET /api/accounts/user - User: anonymous
[2025-06-10T13:00:59.134Z] INFO: Token verification successful for user joonas
[2025-06-10T13:00:59.135Z] REQUEST: GET /api/transactions/account/your_bank_prefix5e351ebe3c5c4937983c - User: anonymous
[2025-06-10T13:00:59.135Z] INFO: User joonas authenticated successfully for GET /user
[2025-06-10T13:00:59.135Z] RESPONSE: GET /account/your_bank_prefix5e351ebe3c5c4937983c - Status: 200
[2025-06-10T13:00:59.135Z] RESPONSE BODY: []
[2025-06-10T13:00:59.133Z] RESPONSE: GET /user - Status: 200
[2025-06-10T13:00:59.133Z] RESPONSE BODY: [{"id":1,"account_number":"your_bank_prefix5e351ebe3c5c4937983c","owner_name":"joonas","balance":1000,"currency":"EUR","user_id":1,"account_type":"savings","created_at":"2025-06-10 12:51:16"},{"id":2,"account_number":"your_bank_prefix15c8e563dbe74995b995","owner_name":"joonas","balance":1000,"currency":"EUR","user_id":1,"account_type":"checking","created_at":"2025-06-10 12:56:21"}]
[2025-06-10T13:00:59.138Z] REQUEST: GET /api/accounts/user - User: anonymous
[2025-06-10T13:00:59.139Z] INFO: Token verification successful for user joonas
[2025-06-10T13:00:59.140Z] INFO: User joonas authenticated successfully for GET /user
[2025-06-10T13:00:59.138Z] RESPONSE: GET /user - Status: 200
[2025-06-10T13:00:59.138Z] RESPONSE BODY: [{"id":1,"account_number":"your_bank_prefix5e351ebe3c5c4937983c","owner_name":"joonas","balance":1000,"currency":"EUR","user_id":1,"account_type":"savings","created_at":"2025-06-10 12:51:16"},{"id":2,"account_number":"your_bank_prefix15c8e563dbe74995b995","owner_name":"joonas","balance":1000,"currency":"EUR","user_id":1,"account_type":"checking","created_at":"2025-06-10 12:56:21"}]
[2025-06-10T13:00:59.146Z] REQUEST: GET /api/transactions/account/your_bank_prefix15c8e563dbe74995b995 - User: anonymous
[2025-06-10T13:00:59.146Z] REQUEST: GET /api/transactions/account/your_bank_prefix5e351ebe3c5c4937983c - User: anonymous
[2025-06-10T13:00:59.146Z] RESPONSE: GET /account/your_bank_prefix15c8e563dbe74995b995 - Status: 200
[2025-06-10T13:00:59.146Z] RESPONSE BODY: []
[2025-06-10T13:00:59.146Z] RESPONSE: GET /account/your_bank_prefix5e351ebe3c5c4937983c - Status: 200
[2025-06-10T13:00:59.146Z] RESPONSE BODY: []
[2025-06-10T13:00:59.150Z] REQUEST: GET /api/transactions/account/your_bank_prefix5e351ebe3c5c4937983c - User: anonymous
[2025-06-10T13:00:59.150Z] RESPONSE: GET /account/your_bank_prefix5e351ebe3c5c4937983c - Status: 200
[2025-06-10T13:00:59.150Z] RESPONSE BODY: []
[2025-06-10T13:00:59.155Z] REQUEST: GET /api/transactions/account/your_bank_prefix15c8e563dbe74995b995 - User: anonymous
[2025-06-10T13:00:59.155Z] RESPONSE: GET /account/your_bank_prefix15c8e563dbe74995b995 - Status: 200
[2025-06-10T13:00:59.155Z] RESPONSE BODY: []
[2025-06-10T13:00:59.157Z] REQUEST: GET /api/transactions/account/your_bank_prefix15c8e563dbe74995b995 - User: anonymous
[2025-06-10T13:00:59.157Z] RESPONSE: GET /account/your_bank_prefix15c8e563dbe74995b995 - Status: 200
[2025-06-10T13:00:59.157Z] RESPONSE BODY: []
[2025-06-10T13:01:05.517Z] REQUEST: GET /api - User: anonymous
[2025-06-10T13:01:05.517Z] RESPONSE: GET / - Status: 200
[2025-06-10T13:01:05.517Z] RESPONSE BODY: {"message":"Welcome to JMB Pank API","environment":"development","documentation":"/api/docs","endpoints":{"users":"/api/users","accounts":"/api/accounts","transactions":"/api/transactions","sessions":"/api/sessions","logs":"/api/logs","test":"/api/test"},"centralBankEndpoints":{"jwksUrl":"http://localhost:3000/api/transactions/jwks","transactionUrl":"http://localhost:3000/api/transactions/b2b"}}
[2025-06-10T13:01:28.269Z] REQUEST: GET /api/logs?lines=100 - User: anonymous
[2025-06-10T13:01:28.269Z] RESPONSE: GET /?lines=100 - Status: 200
[2025-06-10T13:01:28.269Z] RESPONSE BODY: ["[2025-06-10T12:50:02.196Z] INFO: Database initialization started","[2025-06-10T12:50:02.198Z] INFO: JMB Pank application starting up","[2025-06-10T12:50:02.198Z] INFO: Environment: development","[2025-06-10T12:50:02.262Z] INFO: Generating new RSA key pair with ID 1","[2025-06-10T12:50:02.321Z] INFO: RSA key pair generated and stored in memory successfully","[2025-06-10T12:50:02.351Z] INFO: Central Bank Service initialized in PRODUCTION mode","[2025-06-10T12:50:02.352Z] INFO: Using Central Bank URL: https://henno.cfd/central-bank","[2025-06-10T12:50:02.379Z] INFO: Test routes enabled - FOR DEVELOPMENT USE ONLY","[2025-06-10T12:50:02.379Z] INFO: Application initialization completed","[2025-06-10T12:50:02.379Z] WARN: This is a test warning message","[2025-06-10T12:50:02.379Z] ERROR: This is a test error message - Test error","[2025-06-10T12:50:02.379Z] DEBUG: This is a debug message with details about the environment","[2025-06-10T12:50:02.381Z] INFO: JMB Pank server running on port 3000","[2025-06-10T12:50:02.382Z] INFO: API Documentation available at http://localhost:3000/api/docs","[2025-06-10T12:50:02.382Z] INFO: JWKS available at http://localhost:3000/api/transactions/jwks","[2025-06-10T12:50:02.382Z] INFO: RSA key pair generated and stored in memory successfully","[2025-06-10T12:50:02.383Z] INFO: Connected to SQLite database","[2025-06-10T12:50:02.383Z] INFO: Database tables created successfully","[2025-06-10T12:50:04.213Z] INFO: Last used bank prefix: your_bank_prefix","[2025-06-10T12:50:04.213Z] INFO: Bank prefix unchanged: your_bank_prefix","[2025-06-10T12:50:04.382Z] INFO: Sample transaction processed: transfer of €1000 from account JMB123 to account JMB456","[2025-06-10T12:50:04.382Z] INFO: User johndoe logged in successfully","[2025-06-10T12:50:04.382Z] WARN: Failed login attempt for user testuser - Invalid credentials","[2025-06-10T12:50:04.382Z] ERROR: Transaction failed - Insufficient funds","[2025-06-10T12:50:14.119Z] WARN: Authentication failed: Toke... [TRUNCATED]
[2025-06-10T13:01:38.398Z] REQUEST: GET /.well-known/appspecific/com.chrome.devtools.json - User: anonymous
[2025-06-10T13:01:48.225Z] REQUEST: GET /api/logs?lines=100 - User: anonymous
[2025-06-10T13:01:48.225Z] RESPONSE: GET /?lines=100 - Status: 200
[2025-06-10T13:01:48.225Z] RESPONSE BODY: ["[2025-06-10T12:50:02.196Z] INFO: Database initialization started","[2025-06-10T12:50:02.198Z] INFO: JMB Pank application starting up","[2025-06-10T12:50:02.198Z] INFO: Environment: development","[2025-06-10T12:50:02.262Z] INFO: Generating new RSA key pair with ID 1","[2025-06-10T12:50:02.321Z] INFO: RSA key pair generated and stored in memory successfully","[2025-06-10T12:50:02.351Z] INFO: Central Bank Service initialized in PRODUCTION mode","[2025-06-10T12:50:02.352Z] INFO: Using Central Bank URL: https://henno.cfd/central-bank","[2025-06-10T12:50:02.379Z] INFO: Test routes enabled - FOR DEVELOPMENT USE ONLY","[2025-06-10T12:50:02.379Z] INFO: Application initialization completed","[2025-06-10T12:50:02.379Z] WARN: This is a test warning message","[2025-06-10T12:50:02.379Z] ERROR: This is a test error message - Test error","[2025-06-10T12:50:02.379Z] DEBUG: This is a debug message with details about the environment","[2025-06-10T12:50:02.381Z] INFO: JMB Pank server running on port 3000","[2025-06-10T12:50:02.382Z] INFO: API Documentation available at http://localhost:3000/api/docs","[2025-06-10T12:50:02.382Z] INFO: JWKS available at http://localhost:3000/api/transactions/jwks","[2025-06-10T12:50:02.382Z] INFO: RSA key pair generated and stored in memory successfully","[2025-06-10T12:50:02.383Z] INFO: Connected to SQLite database","[2025-06-10T12:50:02.383Z] INFO: Database tables created successfully","[2025-06-10T12:50:04.213Z] INFO: Last used bank prefix: your_bank_prefix","[2025-06-10T12:50:04.213Z] INFO: Bank prefix unchanged: your_bank_prefix","[2025-06-10T12:50:04.382Z] INFO: Sample transaction processed: transfer of €1000 from account JMB123 to account JMB456","[2025-06-10T12:50:04.382Z] INFO: User johndoe logged in successfully","[2025-06-10T12:50:04.382Z] WARN: Failed login attempt for user testuser - Invalid credentials","[2025-06-10T12:50:04.382Z] ERROR: Transaction failed - Insufficient funds","[2025-06-10T12:50:14.119Z] WARN: Authentication failed: Toke... [TRUNCATED]
[2025-06-10T13:01:52.205Z] REQUEST: GET /api/accounts/user - User: anonymous
[2025-06-10T13:01:52.206Z] INFO: Token verification successful for user joonas
[2025-06-10T13:01:52.207Z] INFO: User joonas authenticated successfully for GET /user
[2025-06-10T13:01:52.205Z] RESPONSE: GET /user - Status: 200
[2025-06-10T13:01:52.205Z] RESPONSE BODY: [{"id":1,"account_number":"your_bank_prefix5e351ebe3c5c4937983c","owner_name":"joonas","balance":1000,"currency":"EUR","user_id":1,"account_type":"savings","created_at":"2025-06-10 12:51:16"},{"id":2,"account_number":"your_bank_prefix15c8e563dbe74995b995","owner_name":"joonas","balance":1000,"currency":"EUR","user_id":1,"account_type":"checking","created_at":"2025-06-10 12:56:21"}]
[2025-06-10T13:01:52.209Z] REQUEST: GET /api/accounts/user - User: anonymous
[2025-06-10T13:01:52.210Z] INFO: Token verification successful for user joonas
[2025-06-10T13:01:52.211Z] INFO: User joonas authenticated successfully for GET /user
[2025-06-10T13:01:52.209Z] RESPONSE: GET /user - Status: 200
[2025-06-10T13:01:52.209Z] RESPONSE BODY: [{"id":1,"account_number":"your_bank_prefix5e351ebe3c5c4937983c","owner_name":"joonas","balance":1000,"currency":"EUR","user_id":1,"account_type":"savings","created_at":"2025-06-10 12:51:16"},{"id":2,"account_number":"your_bank_prefix15c8e563dbe74995b995","owner_name":"joonas","balance":1000,"currency":"EUR","user_id":1,"account_type":"checking","created_at":"2025-06-10 12:56:21"}]
[2025-06-10T13:01:52.214Z] REQUEST: GET /api/accounts/user - User: anonymous
[2025-06-10T13:01:52.215Z] INFO: Token verification successful for user joonas
[2025-06-10T13:01:52.216Z] INFO: User joonas authenticated successfully for GET /user
[2025-06-10T13:01:52.214Z] RESPONSE: GET /user - Status: 200
[2025-06-10T13:01:52.214Z] RESPONSE BODY: [{"id":1,"account_number":"your_bank_prefix5e351ebe3c5c4937983c","owner_name":"joonas","balance":1000,"currency":"EUR","user_id":1,"account_type":"savings","created_at":"2025-06-10 12:51:16"},{"id":2,"account_number":"your_bank_prefix15c8e563dbe74995b995","owner_name":"joonas","balance":1000,"currency":"EUR","user_id":1,"account_type":"checking","created_at":"2025-06-10 12:56:21"}]
[2025-06-10T13:01:52.217Z] REQUEST: GET /api/accounts/user - User: anonymous
[2025-06-10T13:01:52.218Z] INFO: Token verification successful for user joonas
[2025-06-10T13:01:52.219Z] INFO: User joonas authenticated successfully for GET /user
[2025-06-10T13:01:52.217Z] RESPONSE: GET /user - Status: 200
[2025-06-10T13:01:52.217Z] RESPONSE BODY: [{"id":1,"account_number":"your_bank_prefix5e351ebe3c5c4937983c","owner_name":"joonas","balance":1000,"currency":"EUR","user_id":1,"account_type":"savings","created_at":"2025-06-10 12:51:16"},{"id":2,"account_number":"your_bank_prefix15c8e563dbe74995b995","owner_name":"joonas","balance":1000,"currency":"EUR","user_id":1,"account_type":"checking","created_at":"2025-06-10 12:56:21"}]
[2025-06-10T13:01:52.221Z] REQUEST: GET /api/accounts/user - User: anonymous
[2025-06-10T13:01:52.222Z] INFO: Token verification successful for user joonas
[2025-06-10T13:01:52.223Z] INFO: User joonas authenticated successfully for GET /user
[2025-06-10T13:01:52.221Z] RESPONSE: GET /user - Status: 200
[2025-06-10T13:01:52.221Z] RESPONSE BODY: [{"id":1,"account_number":"your_bank_prefix5e351ebe3c5c4937983c","owner_name":"joonas","balance":1000,"currency":"EUR","user_id":1,"account_type":"savings","created_at":"2025-06-10 12:51:16"},{"id":2,"account_number":"your_bank_prefix15c8e563dbe74995b995","owner_name":"joonas","balance":1000,"currency":"EUR","user_id":1,"account_type":"checking","created_at":"2025-06-10 12:56:21"}]
[2025-06-10T13:01:52.259Z] REQUEST: GET /api/transactions/account/your_bank_prefix5e351ebe3c5c4937983c - User: anonymous
[2025-06-10T13:01:52.259Z] RESPONSE: GET /account/your_bank_prefix5e351ebe3c5c4937983c - Status: 200
[2025-06-10T13:01:52.259Z] RESPONSE BODY: []
[2025-06-10T13:01:52.262Z] REQUEST: GET /api/transactions/account/your_bank_prefix5e351ebe3c5c4937983c - User: anonymous
[2025-06-10T13:01:52.262Z] RESPONSE: GET /account/your_bank_prefix5e351ebe3c5c4937983c - Status: 200
[2025-06-10T13:01:52.262Z] RESPONSE BODY: []
[2025-06-10T13:01:52.263Z] REQUEST: GET /api/transactions/account/your_bank_prefix5e351ebe3c5c4937983c - User: anonymous
[2025-06-10T13:01:52.263Z] RESPONSE: GET /account/your_bank_prefix5e351ebe3c5c4937983c - Status: 200
[2025-06-10T13:01:52.263Z] RESPONSE BODY: []
[2025-06-10T13:01:52.269Z] REQUEST: GET /api/transactions/account/your_bank_prefix15c8e563dbe74995b995 - User: anonymous
[2025-06-10T13:01:52.269Z] RESPONSE: GET /account/your_bank_prefix15c8e563dbe74995b995 - Status: 200
[2025-06-10T13:01:52.269Z] RESPONSE BODY: []
[2025-06-10T13:01:52.271Z] REQUEST: GET /api/transactions/account/your_bank_prefix15c8e563dbe74995b995 - User: anonymous
[2025-06-10T13:01:52.271Z] RESPONSE: GET /account/your_bank_prefix15c8e563dbe74995b995 - Status: 200
[2025-06-10T13:01:52.271Z] RESPONSE BODY: []
[2025-06-10T13:01:52.273Z] REQUEST: GET /api/transactions/account/your_bank_prefix15c8e563dbe74995b995 - User: anonymous
[2025-06-10T13:01:52.273Z] RESPONSE: GET /account/your_bank_prefix15c8e563dbe74995b995 - Status: 200
[2025-06-10T13:01:52.273Z] RESPONSE BODY: []
[2025-06-10T13:01:53.943Z] REQUEST: GET /api/logs?lines=100 - User: anonymous
[2025-06-10T13:01:53.943Z] RESPONSE: GET /?lines=100 - Status: 200
[2025-06-10T13:01:53.943Z] RESPONSE BODY: ["[2025-06-10T12:50:02.196Z] INFO: Database initialization started","[2025-06-10T12:50:02.198Z] INFO: JMB Pank application starting up","[2025-06-10T12:50:02.198Z] INFO: Environment: development","[2025-06-10T12:50:02.262Z] INFO: Generating new RSA key pair with ID 1","[2025-06-10T12:50:02.321Z] INFO: RSA key pair generated and stored in memory successfully","[2025-06-10T12:50:02.351Z] INFO: Central Bank Service initialized in PRODUCTION mode","[2025-06-10T12:50:02.352Z] INFO: Using Central Bank URL: https://henno.cfd/central-bank","[2025-06-10T12:50:02.379Z] INFO: Test routes enabled - FOR DEVELOPMENT USE ONLY","[2025-06-10T12:50:02.379Z] INFO: Application initialization completed","[2025-06-10T12:50:02.379Z] WARN: This is a test warning message","[2025-06-10T12:50:02.379Z] ERROR: This is a test error message - Test error","[2025-06-10T12:50:02.379Z] DEBUG: This is a debug message with details about the environment","[2025-06-10T12:50:02.381Z] INFO: JMB Pank server running on port 3000","[2025-06-10T12:50:02.382Z] INFO: API Documentation available at http://localhost:3000/api/docs","[2025-06-10T12:50:02.382Z] INFO: JWKS available at http://localhost:3000/api/transactions/jwks","[2025-06-10T12:50:02.382Z] INFO: RSA key pair generated and stored in memory successfully","[2025-06-10T12:50:02.383Z] INFO: Connected to SQLite database","[2025-06-10T12:50:02.383Z] INFO: Database tables created successfully","[2025-06-10T12:50:04.213Z] INFO: Last used bank prefix: your_bank_prefix","[2025-06-10T12:50:04.213Z] INFO: Bank prefix unchanged: your_bank_prefix","[2025-06-10T12:50:04.382Z] INFO: Sample transaction processed: transfer of €1000 from account JMB123 to account JMB456","[2025-06-10T12:50:04.382Z] INFO: User johndoe logged in successfully","[2025-06-10T12:50:04.382Z] WARN: Failed login attempt for user testuser - Invalid credentials","[2025-06-10T12:50:04.382Z] ERROR: Transaction failed - Insufficient funds","[2025-06-10T12:50:14.119Z] WARN: Authentication failed: Toke... [TRUNCATED]
[2025-06-10T13:02:33.825Z] REQUEST: GET /api/logs?lines=100 - User: anonymous
[2025-06-10T13:02:33.825Z] RESPONSE: GET /?lines=100 - Status: 200
[2025-06-10T13:02:33.825Z] RESPONSE BODY: ["[2025-06-10T12:50:02.196Z] INFO: Database initialization started","[2025-06-10T12:50:02.198Z] INFO: JMB Pank application starting up","[2025-06-10T12:50:02.198Z] INFO: Environment: development","[2025-06-10T12:50:02.262Z] INFO: Generating new RSA key pair with ID 1","[2025-06-10T12:50:02.321Z] INFO: RSA key pair generated and stored in memory successfully","[2025-06-10T12:50:02.351Z] INFO: Central Bank Service initialized in PRODUCTION mode","[2025-06-10T12:50:02.352Z] INFO: Using Central Bank URL: https://henno.cfd/central-bank","[2025-06-10T12:50:02.379Z] INFO: Test routes enabled - FOR DEVELOPMENT USE ONLY","[2025-06-10T12:50:02.379Z] INFO: Application initialization completed","[2025-06-10T12:50:02.379Z] WARN: This is a test warning message","[2025-06-10T12:50:02.379Z] ERROR: This is a test error message - Test error","[2025-06-10T12:50:02.379Z] DEBUG: This is a debug message with details about the environment","[2025-06-10T12:50:02.381Z] INFO: JMB Pank server running on port 3000","[2025-06-10T12:50:02.382Z] INFO: API Documentation available at http://localhost:3000/api/docs","[2025-06-10T12:50:02.382Z] INFO: JWKS available at http://localhost:3000/api/transactions/jwks","[2025-06-10T12:50:02.382Z] INFO: RSA key pair generated and stored in memory successfully","[2025-06-10T12:50:02.383Z] INFO: Connected to SQLite database","[2025-06-10T12:50:02.383Z] INFO: Database tables created successfully","[2025-06-10T12:50:04.213Z] INFO: Last used bank prefix: your_bank_prefix","[2025-06-10T12:50:04.213Z] INFO: Bank prefix unchanged: your_bank_prefix","[2025-06-10T12:50:04.382Z] INFO: Sample transaction processed: transfer of €1000 from account JMB123 to account JMB456","[2025-06-10T12:50:04.382Z] INFO: User johndoe logged in successfully","[2025-06-10T12:50:04.382Z] WARN: Failed login attempt for user testuser - Invalid credentials","[2025-06-10T12:50:04.382Z] ERROR: Transaction failed - Insufficient funds","[2025-06-10T12:50:14.119Z] WARN: Authentication failed: Toke... [TRUNCATED]
[2025-06-10T13:03:09.154Z] REQUEST: GET /api/logs?lines=100 - User: anonymous
[2025-06-10T13:03:09.154Z] RESPONSE: GET /?lines=100 - Status: 200
[2025-06-10T13:03:09.154Z] RESPONSE BODY: ["[2025-06-10T12:50:02.196Z] INFO: Database initialization started","[2025-06-10T12:50:02.198Z] INFO: JMB Pank application starting up","[2025-06-10T12:50:02.198Z] INFO: Environment: development","[2025-06-10T12:50:02.262Z] INFO: Generating new RSA key pair with ID 1","[2025-06-10T12:50:02.321Z] INFO: RSA key pair generated and stored in memory successfully","[2025-06-10T12:50:02.351Z] INFO: Central Bank Service initialized in PRODUCTION mode","[2025-06-10T12:50:02.352Z] INFO: Using Central Bank URL: https://henno.cfd/central-bank","[2025-06-10T12:50:02.379Z] INFO: Test routes enabled - FOR DEVELOPMENT USE ONLY","[2025-06-10T12:50:02.379Z] INFO: Application initialization completed","[2025-06-10T12:50:02.379Z] WARN: This is a test warning message","[2025-06-10T12:50:02.379Z] ERROR: This is a test error message - Test error","[2025-06-10T12:50:02.379Z] DEBUG: This is a debug message with details about the environment","[2025-06-10T12:50:02.381Z] INFO: JMB Pank server running on port 3000","[2025-06-10T12:50:02.382Z] INFO: API Documentation available at http://localhost:3000/api/docs","[2025-06-10T12:50:02.382Z] INFO: JWKS available at http://localhost:3000/api/transactions/jwks","[2025-06-10T12:50:02.382Z] INFO: RSA key pair generated and stored in memory successfully","[2025-06-10T12:50:02.383Z] INFO: Connected to SQLite database","[2025-06-10T12:50:02.383Z] INFO: Database tables created successfully","[2025-06-10T12:50:04.213Z] INFO: Last used bank prefix: your_bank_prefix","[2025-06-10T12:50:04.213Z] INFO: Bank prefix unchanged: your_bank_prefix","[2025-06-10T12:50:04.382Z] INFO: Sample transaction processed: transfer of €1000 from account JMB123 to account JMB456","[2025-06-10T12:50:04.382Z] INFO: User johndoe logged in successfully","[2025-06-10T12:50:04.382Z] WARN: Failed login attempt for user testuser - Invalid credentials","[2025-06-10T12:50:04.382Z] ERROR: Transaction failed - Insufficient funds","[2025-06-10T12:50:14.119Z] WARN: Authentication failed: Toke... [TRUNCATED]
